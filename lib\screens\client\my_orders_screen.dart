import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/order_model.dart';
import '../../providers/language_provider.dart';
import '../../providers/demo_auth_provider.dart';
import '../../providers/theme_provider.dart';
import '../../services/order_service.dart';
import '../../utils/app_localizations.dart';
import '../chat/chat_screen.dart';

import 'submit_review_screen.dart';
import '../../models/user_model.dart';

class MyOrdersScreen extends StatefulWidget {
  const MyOrdersScreen({super.key});

  @override
  State<MyOrdersScreen> createState() => _MyOrdersScreenState();
}

class _MyOrdersScreenState extends State<MyOrdersScreen> {
  List<OrderModel> _orders = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadOrders();
  }

  Future<void> _loadOrders() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final authProvider = Provider.of<DemoAuthProvider>(context, listen: false);
      final userId = authProvider.user?.id;

      if (userId != null) {
        final orders = await OrderService.getOrders(clientId: userId);
        setState(() {
          _orders = orders;
          _isLoading = false;
        });
      } else {
        setState(() {
          _orders = [];
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _orders = [];
        _isLoading = false;
      });
    }
  }

  List<OrderModel> _getOrdersByStatus(OrderStatus status) {
    return _orders.where((order) => order.status == status).toList();
  }

  @override
  Widget build(BuildContext context) {
    final languageProvider = Provider.of<LanguageProvider>(context);

    // Handle null localization for testing
    AppLocalizations? l10n;
    try {
      l10n = AppLocalizations.of(context);
    } catch (e) {
      // Localization not available in test environment
      l10n = null;
    }

    if (l10n == null) {
      return const Scaffold(body: Center(child: Text('Localization not available')));
    }

    return DefaultTabController(
      length: 4,
      child: Directionality(
        textDirection: languageProvider.textDirection,
        child: Scaffold(
          body: Column(
            children: [
              // Header matching app's consistent design system
              Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Theme.of(context).colorScheme.primary,
                      Theme.of(context).colorScheme.primary.withValues(alpha: 0.8),
                    ],
                  ),
                ),
                child: SafeArea(
                  top: false,
                  child: Padding(
                    padding: const EdgeInsets.only(top: 20),
                    child: Column(
                      children: [
                        // Title Section - centered
                        Container(
                          height: 56,
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          child: Center(
                            child: Text(
                              languageProvider.isArabic ? 'طلباتي' : 'My Orders',
                              style: const TextStyle(fontWeight: FontWeight.bold, color: Colors.white, fontSize: 20),
                            ),
                          ),
                        ),
                        // Tab Bar Section
                        TabBar(
                          labelColor: Colors.white,
                          unselectedLabelColor: Colors.white.withValues(alpha: 0.7),
                          indicatorColor: Colors.white,
                          indicatorWeight: 3,
                          labelStyle: const TextStyle(fontSize: 12, fontWeight: FontWeight.w600),
                          unselectedLabelStyle: const TextStyle(fontSize: 11, fontWeight: FontWeight.w500),
                          tabs: [
                            Tab(
                              icon: const Icon(Icons.assignment, size: 20),
                              text: languageProvider.isArabic ? 'نشطة' : 'Active',
                            ),
                            Tab(
                              icon: const Icon(Icons.upload, size: 20),
                              text: languageProvider.isArabic ? 'مُسلَّمة' : 'Delivered',
                            ),
                            Tab(
                              icon: const Icon(Icons.check_circle, size: 20),
                              text: languageProvider.isArabic ? 'مكتملة' : 'Completed',
                            ),
                            Tab(
                              icon: const Icon(Icons.cancel, size: 20),
                              text: languageProvider.isArabic ? 'ملغية' : 'Cancelled',
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              // Content Section
              Expanded(
                child:
                    _isLoading
                        ? const Center(child: CircularProgressIndicator())
                        : TabBarView(
                          children: [
                            _buildActiveOrdersList(l10n, languageProvider),
                            _buildOrdersList(OrderStatus.delivered, l10n, languageProvider),
                            _buildOrdersList(OrderStatus.completed, l10n, languageProvider),
                            _buildOrdersList(OrderStatus.cancelled, l10n, languageProvider),
                          ],
                        ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildActiveOrdersList(AppLocalizations l10n, LanguageProvider languageProvider) {
    final activeOrders =
        _orders
            .where((order) => order.status != OrderStatus.completed && order.status != OrderStatus.cancelled)
            .toList();

    if (activeOrders.isEmpty) {
      return _buildDemoOrdersList(languageProvider, 'active');
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: activeOrders.length,
      itemBuilder: (context, index) {
        final order = activeOrders[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ListTile(
            title: Text('Order #${order.id}'),
            subtitle: Text('Request: ${order.requestId}'),
            trailing: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(_getStatusIcon(order.status)),
                Text(_getStatusText(order.status, languageProvider.isArabic)),
              ],
            ),
            onTap: () {
              print('=== DEBUG: My Orders - Order card tapped ===');
              print('Order ID: ${order.id}');
              print('Order Status: ${order.status}');
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder:
                      (context) => ChatScreen(
                        chatId: 'order_${order.id}',
                        recipientName: 'Freelancer',
                        requestTitle: 'Order #${order.id}',
                        orderId: order.id, // Pass the order ID
                      ),
                ),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildOrdersList(OrderStatus status, AppLocalizations l10n, LanguageProvider languageProvider) {
    final orders = _getOrdersByStatus(status);

    if (orders.isEmpty) {
      return _buildDemoOrdersList(languageProvider, status.toString().split('.').last);
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: orders.length,
      itemBuilder: (context, index) {
        final order = orders[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 12),
          child: ListTile(
            title: Text('Order #${order.id}'),
            subtitle: Text('Request: ${order.requestId}'),
            trailing: Column(
              mainAxisSize: MainAxisSize.min,
              children: [Icon(_getStatusIcon(status)), Text(_getStatusText(status, languageProvider.isArabic))],
            ),
            onTap: () {
              print('=== DEBUG: My Orders - Order card tapped ===');
              print('Order ID: ${order.id}');
              print('Order Status: ${order.status}');
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder:
                      (context) => ChatScreen(
                        chatId: 'order_${order.id}',
                        recipientName: 'Freelancer',
                        requestTitle: 'Order #${order.id}',
                        orderId: order.id, // Pass the order ID
                      ),
                ),
              );
            },
          ),
        );
      },
    );
  }

  IconData _getStatusIcon(OrderStatus status) {
    switch (status) {
      case OrderStatus.delivered:
        return Icons.check_circle;
      case OrderStatus.completed:
        return Icons.check_circle;
      case OrderStatus.cancelled:
        return Icons.cancel;
      case OrderStatus.inProgress:
        return Icons.work;
      case OrderStatus.editing:
        return Icons.edit;
      default:
        return Icons.assignment;
    }
  }

  String _getStatusText(OrderStatus status, bool isArabic) {
    switch (status) {
      case OrderStatus.created:
        return isArabic ? 'تم الإنشاء' : 'Created';

      case OrderStatus.paymentConfirmed:
        return isArabic ? 'تم تأكيد الدفع' : 'Payment Confirmed';
      case OrderStatus.inProgress:
        return isArabic ? 'قيد التنفيذ' : 'In Progress';
      case OrderStatus.submitted:
        return isArabic ? 'تم التسليم للمراجعة' : 'Submitted for Review';
      case OrderStatus.delivered:
        return isArabic ? 'مُسلَّم' : 'Delivered';
      case OrderStatus.editing:
        return isArabic ? 'قيد التعديل' : 'Editing';
      case OrderStatus.completed:
        return isArabic ? 'مكتمل' : 'Completed';
      case OrderStatus.cancelled:
        return isArabic ? 'ملغي' : 'Cancelled';
      case OrderStatus.paymentPending:
        return isArabic ? 'قيد التنفيذ' : 'In Progress';
    }
  }

  // Demo functionality
  Widget _buildDemoOrdersList(LanguageProvider languageProvider, String tabType) {
    final isArabic = languageProvider.isArabic;
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        // MASSIVE TEST HEADER - IMPOSSIBLE TO MISS
        Container(
          padding: const EdgeInsets.all(20),
          margin: const EdgeInsets.only(bottom: 16),
          decoration: BoxDecoration(
            color: Colors.purple, // BRIGHT PURPLE
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.yellow, width: 5), // THICK YELLOW BORDER
          ),
          child: Row(
            children: [
              const Icon(Icons.preview, color: Colors.orange, size: 24),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '🟣🟡 PURPLE HEADER TEST 🟡🟣',
                      style: TextStyle(fontWeight: FontWeight.bold, color: Colors.white, fontSize: 20),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      isArabic
                          ? 'هذه أمثلة على كيفية ظهور الطلبات في هذا القسم'
                          : 'These are examples of how orders will appear in this section',
                      style: TextStyle(color: Colors.orange.shade600, fontSize: 12),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),

        // Demo cards based on tab type
        ..._getDemoOrdersForTab(tabType, isArabic, isDark),
      ],
    );
  }

  List<Widget> _getDemoOrdersForTab(String tabType, bool isArabic, bool isDark) {
    switch (tabType) {
      case 'active':
        return [
          _buildDemoOrderCard(context, _createDemoOrder('ORD001', 150.0, OrderStatus.inProgress), isArabic, isDark),
          _buildDemoOrderCard(context, _createDemoOrder('ORD002', 300.0, OrderStatus.inProgress), isArabic, isDark),
          _buildDemoOrderCard(context, _createDemoOrder('ORD003', 75.0, OrderStatus.submitted), isArabic, isDark),
        ];
      case 'delivered':
        return [
          _buildDemoOrderCard(context, _createDemoOrder('ORD004', 200.0, OrderStatus.delivered), isArabic, isDark),
        ];
      case 'completed':
        return [
          _buildDemoOrderCard(context, _createDemoOrder('ORD006', 250.0, OrderStatus.completed), isArabic, isDark),
        ];
      case 'cancelled':
        return [
          _buildDemoOrderCard(context, _createDemoOrder('ORD007', 100.0, OrderStatus.cancelled), isArabic, isDark),
        ];
      default:
        return [
          _buildDemoOrderCard(context, _createDemoOrder('ORD001', 150.0, OrderStatus.inProgress), isArabic, isDark),
        ];
    }
  }

  OrderModel _createDemoOrder(String id, double amount, OrderStatus status) {
    return OrderModel(
      id: id,
      clientId: 'client123',
      freelancerId: 'freelancer456',
      requestId: 'req$id',
      offerId: 'offer$id',
      amount: amount,
      status: status,
      createdAt: DateTime.now().subtract(Duration(days: (id.hashCode % 10) + 1)),
      updatedAt: DateTime.now().subtract(Duration(hours: (id.hashCode % 24) + 1)),
    );
  }

  Widget _buildDemoOrderCard(BuildContext context, OrderModel order, bool isArabic, bool isDark) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Row
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _getServiceTitle(order.id, isArabic),
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: isDark ? Colors.white : Colors.grey[900],
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        isArabic ? 'طلب #${order.id}' : 'Order #${order.id}',
                        style: TextStyle(fontSize: 14, color: isDark ? Colors.grey[400] : Colors.grey[600]),
                      ),
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      '${order.amount.toStringAsFixed(0)} ريال',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: ThemeProvider.successGreen,
                      ),
                    ),
                    const SizedBox(height: 4),
                    _buildStatusBadge(order.status, isArabic),
                  ],
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Action Buttons Row
            Row(
              children: [
                Expanded(child: _buildActionButton(order.status, isArabic, order)),
                const SizedBox(width: 12),
                _buildChatButton(isArabic, order),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Simple Status Badge
  Widget _buildStatusBadge(OrderStatus status, bool isArabic) {
    Color color;
    String text;

    switch (status) {
      case OrderStatus.paymentPending:
        color = Colors.orange;
        text = isArabic ? 'في انتظار الدفع' : 'Payment Pending';
        break;
      case OrderStatus.inProgress:
        color = Colors.blue;
        text = isArabic ? 'قيد التنفيذ' : 'In Progress';
        break;
      case OrderStatus.submitted:
        color = Colors.purple;
        text = isArabic ? 'تم التسليم' : 'Submitted';
        break;
      case OrderStatus.delivered:
        color = Colors.teal;
        text = isArabic ? 'مُسلَّم' : 'Delivered';
        break;
      case OrderStatus.completed:
        color = Colors.green;
        text = isArabic ? 'مكتمل' : 'Completed';
        break;
      case OrderStatus.cancelled:
        color = Colors.red;
        text = isArabic ? 'ملغي' : 'Cancelled';
        break;
      default:
        color = Colors.grey;
        text = isArabic ? 'غير محدد' : 'Unknown';
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color, width: 1),
      ),
      child: Text(text, style: TextStyle(color: color, fontSize: 12, fontWeight: FontWeight.w600)),
    );
  }

  // Simple Action Button
  Widget _buildActionButton(OrderStatus status, bool isArabic, OrderModel order) {
    switch (status) {
      case OrderStatus.paymentPending:
        return ElevatedButton.icon(
          onPressed: () => _handlePayNow(context, isArabic, order),
          icon: const Icon(Icons.payment, size: 16),
          label: Text(isArabic ? 'ادفع الآن' : 'Pay Now'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.green,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 12),
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          ),
        );
      case OrderStatus.delivered:
        return ElevatedButton.icon(
          onPressed: () => _handleAcceptWork(context, isArabic, order),
          icon: const Icon(Icons.check, size: 16),
          label: Text(isArabic ? 'قبول العمل' : 'Accept Work'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.green,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 12),
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          ),
        );
      case OrderStatus.completed:
        return ElevatedButton.icon(
          onPressed: () => _handleRateFreelancer(context, isArabic, order),
          icon: const Icon(Icons.star, size: 16),
          label: Text(isArabic ? 'تقييم' : 'Rate'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.orange,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 12),
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          ),
        );
      default:
        return ElevatedButton.icon(
          onPressed: () => _handleViewDetails(context, isArabic, order),
          icon: const Icon(Icons.visibility, size: 16),
          label: Text(isArabic ? 'التفاصيل' : 'Details'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.grey,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 12),
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          ),
        );
    }
  }

  // Simple Chat Button
  Widget _buildChatButton(bool isArabic, OrderModel order) {
    return IconButton(
      onPressed: () => _handleOpenChat(context, isArabic, order),
      icon: const Icon(Icons.chat_bubble_outline),
      style: IconButton.styleFrom(
        backgroundColor: Colors.blue.withValues(alpha: 0.1),
        foregroundColor: Colors.blue,
        padding: const EdgeInsets.all(12),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
      tooltip: isArabic ? 'محادثة' : 'Chat',
    );
  }

  String _getServiceTitle(String orderId, bool isArabic) {
    final titles = {
      'ORD001': isArabic ? 'تصميم شعار للشركة' : 'Logo Design for Company',
      'ORD002': isArabic ? 'تطوير موقع إلكتروني' : 'Website Development',
      'ORD003': isArabic ? 'ترجمة مقال' : 'Article Translation',
      'ORD004': isArabic ? 'تحليل البيانات' : 'Data Analysis',
      'ORD005': isArabic ? 'كتابة محتوى' : 'Content Writing',
      'ORD006': isArabic ? 'تصميم تطبيق' : 'App Design',
      'ORD007': isArabic ? 'مراجعة نص' : 'Text Review',
    };
    return titles[orderId] ?? (isArabic ? 'خدمة عامة' : 'General Service');
  }

  // Button Handler Methods
  void _handlePayNow(BuildContext context, bool isArabic, [OrderModel? order]) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(isArabic ? 'تأكيد الدفع' : 'Confirm Payment'),
            content: Text(
              isArabic
                  ? 'هل تريد المتابعة لدفع ${order?.amount.toStringAsFixed(0) ?? '0'} ريال؟'
                  : 'Do you want to proceed with payment of ${order?.amount.toStringAsFixed(0) ?? '0'} SAR?',
            ),
            actions: [
              TextButton(onPressed: () => Navigator.of(context).pop(), child: Text(isArabic ? 'إلغاء' : 'Cancel')),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(isArabic ? 'تم بدء عملية الدفع!' : 'Payment process started!'),
                      backgroundColor: Colors.green,
                    ),
                  );
                },
                child: Text(isArabic ? 'ادفع الآن' : 'Pay Now'),
              ),
            ],
          ),
    );
  }

  void _handleAcceptWork(BuildContext context, bool isArabic, [OrderModel? order]) {
    print('=== Accept Work Action Triggered ===');
    print('Order ID: ${order?.id ?? 'demo_order'}');

    // Show immediate feedback
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(isArabic ? 'فتح حوار قبول العمل...' : 'Opening accept work dialog...'),
        backgroundColor: Colors.blue,
      ),
    );

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(isArabic ? 'قبول العمل' : 'Accept Work'),
          content: Text(
            isArabic
                ? 'هل أنت متأكد من قبول هذا العمل؟ سيتم تحديث حالة الطلب إلى مكتمل.'
                : 'Are you sure you want to accept this work? The order status will be updated to completed.',
          ),
          actions: [
            TextButton(onPressed: () => Navigator.of(context).pop(), child: Text(isArabic ? 'إلغاء' : 'Cancel')),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(isArabic ? 'تم قبول العمل بنجاح!' : 'Work accepted successfully!'),
                    backgroundColor: Colors.green,
                  ),
                );
              },
              child: Text(isArabic ? 'قبول' : 'Accept'),
            ),
          ],
        );
      },
    );
  }

  void _handleRateFreelancer(BuildContext context, bool isArabic, [OrderModel? order]) {
    print('=== Rate Freelancer Action Triggered ===');
    print('Order ID: ${order?.id ?? 'demo_order'}');

    // Create a demo freelancer for rating
    final demoFreelancer = UserModel(
      id: 'freelancer_demo',
      email: '<EMAIL>',
      fullName: isArabic ? 'أحمد محمد' : 'Ahmed Mohamed',
      role: UserRole.freelancer,
      isVerified: true,
      createdAt: DateTime.now(),
    );

    // Create a demo order for rating
    final demoOrder = OrderModel(
      id: 'demo_order_001',
      clientId: 'client_demo',
      freelancerId: 'freelancer_demo',
      requestId: 'request_demo',
      offerId: 'offer_demo',
      amount: 250.0,
      status: OrderStatus.completed,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    try {
      Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => SubmitReviewScreen(order: demoOrder, freelancer: demoFreelancer)),
      );
    } catch (e) {
      print('Error navigating to SubmitReviewScreen: $e');
      ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Error: $e')));
    }
  }

  void _handleViewDetails(BuildContext context, bool isArabic, [OrderModel? order]) {
    print('=== View Details Action Triggered ===');
    print('Order ID: ${order?.id ?? 'demo_order_001'}');

    final orderId = order?.id ?? 'demo_order_001';

    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => ChatScreen(
              chatId: 'details_$orderId',
              recipientName: isArabic ? 'المستقل' : 'Freelancer',
              requestTitle: isArabic ? 'تفاصيل الطلب #$orderId' : 'Order Details #$orderId',
              orderId: orderId,
            ),
      ),
    );
  }

  void _handleOpenChat(BuildContext context, bool isArabic, [OrderModel? order]) {
    print('=== Open Chat Action Triggered ===');
    print('Order ID: ${order?.id ?? 'demo_order_001'}');

    final orderId = order?.id ?? 'demo_order_001';

    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => ChatScreen(
              chatId: 'chat_$orderId',
              recipientName: isArabic ? 'المستقل' : 'Freelancer',
              requestTitle: isArabic ? 'محادثة الطلب #$orderId' : 'Order Chat #$orderId',
              orderId: orderId,
            ),
      ),
    );
  }
}
