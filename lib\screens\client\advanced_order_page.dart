import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import '../../models/order_model.dart';
import '../../models/service_request_model.dart';
import '../../providers/language_provider.dart';
import '../../providers/theme_provider.dart';
import '../../providers/demo_auth_provider.dart';
import '../../services/order_service.dart';
import '../../services/request_service.dart';
import '../../services/storage_service.dart';
import '../../models/user_model.dart';
import '../../utils/app_localizations.dart';
import '../../widgets/common/enhanced_widgets.dart';
import '../../widgets/notifications/notification_widgets.dart';
import '../notifications/notifications_screen.dart';

import '../chat/chat_screen.dart';
import 'submit_review_screen.dart';

class StatusInfo {
  final IconData icon;
  final String label;
  final Color color;

  StatusInfo({required this.icon, required this.label, required this.color});
}

class AdvancedOrderPage extends StatefulWidget {
  const AdvancedOrderPage({super.key});

  @override
  State<AdvancedOrderPage> createState() => _AdvancedOrderPageState();
}

enum OrderStatusFilter { waiting, active, completed, cancelled }

class _AdvancedOrderPageState extends State<AdvancedOrderPage> with TickerProviderStateMixin {
  List<OrderModel> _orders = [];
  List<ServiceRequestModel> _pendingRequests = [];
  bool _isLoading = true;
  late TabController _tabController;
  OrderStatusFilter _selectedStatus = OrderStatusFilter.waiting;
  OrderStatusFilter? _pressedStatus;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadOrders();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadOrders() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final authProvider = Provider.of<DemoAuthProvider>(context, listen: false);
      final userId = authProvider.user?.id;

      if (userId != null) {
        final orders = await OrderService.getOrders(clientId: userId);
        final pendingRequests = await RequestService.getRequests(clientId: userId, status: RequestStatus.pending);
        setState(() {
          _orders = orders;
          _pendingRequests = pendingRequests;
          _isLoading = false;
        });
      } else {
        setState(() {
          _orders = [];
          _pendingRequests = [];
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _orders = [];
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer2<LanguageProvider, ThemeProvider>(
      builder: (context, languageProvider, themeProvider, child) {
        final isArabic = languageProvider.isArabic;
        final isDark = themeProvider.isDarkMode;
        final l10n = AppLocalizations.of(context);

        return Directionality(
          textDirection: languageProvider.textDirection,
          child: Scaffold(
            backgroundColor: isDark ? ThemeProvider.darkBackground : ThemeProvider.lightBackground,
            appBar: AppBar(
              title: Text(isArabic ? 'طلباتي' : 'My Orders'),
              backgroundColor: isDark ? ThemeProvider.darkCardBackground : Colors.white,
              foregroundColor: isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
              elevation: 0,
              actions: [
                NotificationBell(
                  onTap: () {
                    Navigator.push(context, MaterialPageRoute(builder: (context) => const NotificationsScreen()));
                  },
                ),
              ],
            ),
            body: Column(
              children: [
                // Enhanced Header Section
                Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Theme.of(context).colorScheme.surface,
                        Theme.of(context).colorScheme.surface.withValues(alpha: 0.95),
                      ],
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                    ),
                    boxShadow: [
                      BoxShadow(color: Colors.black.withValues(alpha: 0.08), blurRadius: 8, offset: const Offset(0, 2)),
                    ],
                  ),
                  child: Column(
                    children: [
                      // Enhanced Filter Bar
                      _buildEnhancedFilterBar(languageProvider),
                    ],
                  ),
                ),

                // Content
                Expanded(
                  child:
                      _isLoading
                          ? const Center(child: CircularProgressIndicator())
                          : AnimatedSwitcher(
                            duration: const Duration(milliseconds: 120),
                            switchInCurve: Curves.easeIn,
                            switchOutCurve: Curves.easeOut,
                            child: _buildContentForStatus(_selectedStatus, isArabic, isDark, l10n),
                          ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // Get orders by category
  List<OrderModel> _getInProgressOrders() {
    return _orders
        .where(
          (order) =>
              order.status == OrderStatus.created ||
              order.status == OrderStatus.paymentPending ||
              order.status == OrderStatus.paymentConfirmed ||
              order.status == OrderStatus.inProgress ||
              order.status == OrderStatus.editing,
        )
        .toList();
  }

  List<OrderModel> _getCompletedAndDeliveredOrders() {
    return _orders
        .where((order) => order.status == OrderStatus.delivered || order.status == OrderStatus.completed)
        .toList();
  }

  List<OrderModel> _getCancelledOrders() {
    return _orders.where((order) => order.status == OrderStatus.cancelled).toList();
  }

  String _safeSubstring(String text, int maxLength) {
    if (text.length <= maxLength) {
      return text;
    }
    return text.substring(0, maxLength);
  }

  // Build Enhanced Filter Bar
  Widget _buildEnhancedFilterBar(LanguageProvider languageProvider) {
    final statuses = [
      OrderStatusFilter.waiting,
      OrderStatusFilter.active,
      OrderStatusFilter.completed,
      OrderStatusFilter.cancelled,
    ];

    return Container(
      padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
      child: Container(
        padding: const EdgeInsets.all(4),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface.withValues(alpha: 0.8),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2)),
          boxShadow: [
            BoxShadow(color: Colors.black.withValues(alpha: 0.05), blurRadius: 8, offset: const Offset(0, 2)),
          ],
        ),
        child: Row(
          children:
              statuses.map((status) {
                final isSelected = _selectedStatus == status;
                final isPressed = _pressedStatus == status;
                final count = _getCountForStatus(status);
                final statusInfo = _getStatusInfo(status, languageProvider);

                return Expanded(
                  child: GestureDetector(
                    onTapDown: (_) {
                      setState(() {
                        _pressedStatus = status;
                      });
                    },
                    onTapUp: (_) {
                      setState(() {
                        _pressedStatus = null;
                      });
                    },
                    onTapCancel: () {
                      setState(() {
                        _pressedStatus = null;
                      });
                    },
                    onTap: () {
                      if (_selectedStatus != status) {
                        HapticFeedback.lightImpact();
                        setState(() {
                          _selectedStatus = status;
                          _pressedStatus = null;
                        });
                      }
                    },
                    behavior: HitTestBehavior.opaque,
                    child: AnimatedContainer(
                      duration: const Duration(milliseconds: 100),
                      curve: Curves.easeOut,
                      margin: const EdgeInsets.symmetric(horizontal: 2),
                      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
                      decoration: BoxDecoration(
                        gradient:
                            isSelected
                                ? LinearGradient(
                                  colors: [
                                    statusInfo.color.withValues(alpha: 0.7),
                                    statusInfo.color.withValues(alpha: 0.5),
                                  ],
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                )
                                : isPressed
                                ? LinearGradient(
                                  colors: [
                                    statusInfo.color.withValues(alpha: 0.2),
                                    statusInfo.color.withValues(alpha: 0.1),
                                  ],
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                )
                                : null,
                        color: isSelected || isPressed ? null : Colors.transparent,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow:
                            isSelected
                                ? [
                                  BoxShadow(
                                    color: statusInfo.color.withValues(alpha: 0.3),
                                    blurRadius: 8,
                                    offset: const Offset(0, 2),
                                  ),
                                ]
                                : isPressed
                                ? [
                                  BoxShadow(
                                    color: statusInfo.color.withValues(alpha: 0.2),
                                    blurRadius: 4,
                                    offset: const Offset(0, 1),
                                  ),
                                ]
                                : null,
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            statusInfo.icon,
                            size: 20,
                            color: isSelected ? Colors.white : statusInfo.color.withValues(alpha: 0.7),
                          ),
                          const SizedBox(height: 4),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color:
                                  isSelected
                                      ? Colors.white.withValues(alpha: 0.2)
                                      : statusInfo.color.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: Text(
                              count.toString(),
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                                color: isSelected ? Colors.white : statusInfo.color,
                              ),
                            ),
                          ),
                          const SizedBox(height: 2),
                          Text(
                            statusInfo.label,
                            style: TextStyle(
                              fontSize: 9,
                              fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                              color:
                                  isSelected
                                      ? Colors.white.withValues(alpha: 0.9)
                                      : Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                            ),
                            textAlign: TextAlign.center,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              }).toList(),
        ),
      ),
    );
  }

  // Get count for status
  int _getCountForStatus(OrderStatusFilter status) {
    switch (status) {
      case OrderStatusFilter.waiting:
        return _pendingRequests.length;
      case OrderStatusFilter.active:
        return _getInProgressOrders().length;
      case OrderStatusFilter.completed:
        return _getCompletedAndDeliveredOrders().length;
      case OrderStatusFilter.cancelled:
        return _getCancelledOrders().length;
    }
  }

  // Get status info
  StatusInfo _getStatusInfo(OrderStatusFilter status, LanguageProvider languageProvider) {
    final isArabic = languageProvider.isArabic;

    switch (status) {
      case OrderStatusFilter.waiting:
        return StatusInfo(
          icon: Icons.hourglass_empty,
          label: isArabic ? 'انتظار' : 'Waiting',
          color: ThemeProvider.primaryBlue,
        );
      case OrderStatusFilter.active:
        return StatusInfo(icon: Icons.work, label: isArabic ? 'قيد التنفيذ' : 'Active', color: Colors.purple);
      case OrderStatusFilter.completed:
        return StatusInfo(
          icon: Icons.check_circle,
          label: isArabic ? 'مكتمل' : 'Done',
          color: ThemeProvider.successGreen,
        );
      case OrderStatusFilter.cancelled:
        return StatusInfo(icon: Icons.cancel, label: isArabic ? 'ملغي' : 'Cancelled', color: Colors.red);
    }
  }

  // Build content for selected status
  Widget _buildContentForStatus(OrderStatusFilter status, bool isArabic, bool isDark, AppLocalizations l10n) {
    switch (status) {
      case OrderStatusFilter.waiting:
        return Container(key: const ValueKey('waiting'), child: _buildPendingRequestsList(isArabic, isDark, l10n));
      case OrderStatusFilter.active:
        return Container(
          key: const ValueKey('active'),
          child: _buildOrdersList(_getInProgressOrders(), isArabic, isDark, l10n),
        );
      case OrderStatusFilter.completed:
        return Container(
          key: const ValueKey('completed'),
          child: _buildOrdersList(_getCompletedAndDeliveredOrders(), isArabic, isDark, l10n),
        );
      case OrderStatusFilter.cancelled:
        return Container(
          key: const ValueKey('cancelled'),
          child: _buildOrdersList(_getCancelledOrders(), isArabic, isDark, l10n),
        );
    }
  }

  // Build Orders List
  Widget _buildOrdersList(List<OrderModel> orders, bool isArabic, bool isDark, AppLocalizations l10n) {
    if (orders.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.inbox_outlined, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              _getEmptyStateText(_selectedStatus, isArabic),
              style: TextStyle(fontSize: 18, color: Colors.grey[600], fontWeight: FontWeight.w500),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadOrders,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: orders.length,
        itemBuilder: (context, index) {
          final order = orders[index];
          return _buildEnhancedOrderCard(order, isArabic, isDark, l10n);
        },
      ),
    );
  }

  // Build Pending Requests List
  Widget _buildPendingRequestsList(bool isArabic, bool isDark, AppLocalizations l10n) {
    if (_pendingRequests.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.inbox_outlined, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              isArabic ? 'لا توجد طلبات في الانتظار' : 'No pending requests',
              style: TextStyle(fontSize: 18, color: Colors.grey[600], fontWeight: FontWeight.w500),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadOrders,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _pendingRequests.length,
        itemBuilder: (context, index) {
          final request = _pendingRequests[index];
          return _buildPendingRequestCard(request, isArabic, isDark);
        },
      ),
    );
  }

  String _getEmptyStateText(OrderStatusFilter status, bool isArabic) {
    switch (status) {
      case OrderStatusFilter.waiting:
        return isArabic ? 'لا توجد طلبات في الانتظار' : 'No pending requests';
      case OrderStatusFilter.active:
        return isArabic ? 'لا توجد طلبات نشطة' : 'No active orders';
      case OrderStatusFilter.completed:
        return isArabic ? 'لا توجد طلبات مكتملة' : 'No completed orders';
      case OrderStatusFilter.cancelled:
        return isArabic ? 'لا توجد طلبات ملغية' : 'No cancelled orders';
    }
  }

  // Build Pending Request Card
  Widget _buildPendingRequestCard(ServiceRequestModel request, bool isArabic, bool isDark) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: isDark ? ThemeProvider.darkCardBackground : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: isDark ? 0.3 : 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with title and status
            Row(
              children: [
                Expanded(
                  child: Text(
                    request.title,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.orange.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(Icons.hourglass_empty, size: 16, color: Colors.orange),
                      const SizedBox(width: 6),
                      Text(
                        isArabic ? 'في انتظار العروض' : 'Waiting for Offers',
                        style: const TextStyle(color: Colors.orange, fontWeight: FontWeight.bold, fontSize: 12),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // Description
            Text(
              request.description,
              style: TextStyle(fontSize: 14, color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600]),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),

            const SizedBox(height: 12),

            // Category and deadline
            Row(
              children: [
                if (request.category != null) ...[
                  Icon(Icons.category, size: 16, color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[500]),
                  const SizedBox(width: 4),
                  Text(
                    request.category!,
                    style: TextStyle(fontSize: 12, color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600]),
                  ),
                  const SizedBox(width: 16),
                ],
                Icon(Icons.schedule, size: 16, color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[500]),
                const SizedBox(width: 4),
                Text(
                  isArabic
                      ? 'الموعد النهائي: ${_formatDate(request.deadline)}'
                      : 'Deadline: ${_formatDate(request.deadline)}',
                  style: TextStyle(fontSize: 12, color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600]),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // Posted time
            Text(
              isArabic ? 'تم النشر ${_getTimeAgo(request.createdAt)}' : 'Posted ${_getTimeAgo(request.createdAt)}',
              style: TextStyle(fontSize: 12, color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[500]),
            ),
          ],
        ),
      ),
    );
  }

  // Build Enhanced Order Card
  Widget _buildEnhancedOrderCard(OrderModel order, bool isArabic, bool isDark, AppLocalizations l10n) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: EnhancedCard(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Row
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        isArabic ? 'طلب #${_safeSubstring(order.id, 8)}' : 'Order #${_safeSubstring(order.id, 8)}',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '\$${order.amount.toStringAsFixed(2)}',
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: ThemeProvider.primaryBlue,
                        ),
                      ),
                    ],
                  ),
                ),
                _buildStatusBadge(order.status, isArabic),
              ],
            ),

            const SizedBox(height: 16),

            // Freelancer Info
            _buildFreelancerInfo(order, isArabic, isDark),

            const SizedBox(height: 16),

            // Progress Timeline
            _buildMiniTimeline(order, isArabic, isDark),

            const SizedBox(height: 16),

            // Last Updated
            Row(
              children: [
                Icon(Icons.access_time, size: 16, color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600]),
                const SizedBox(width: 8),
                Text(
                  isArabic
                      ? 'آخر تحديث: ${_formatDateTime(order.updatedAt ?? order.createdAt, isArabic)}'
                      : 'Last updated: ${_formatDateTime(order.updatedAt ?? order.createdAt, isArabic)}',
                  style: TextStyle(fontSize: 12, color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600]),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Action Buttons
            _buildOrderActionButtons(order, isArabic, isDark, l10n),
          ],
        ),
      ),
    );
  }

  // Build Status Badge
  Widget _buildStatusBadge(OrderStatus status, bool isArabic) {
    Color color;
    String text;
    IconData icon;

    switch (status) {
      case OrderStatus.created:
      case OrderStatus.paymentPending:
        color = ThemeProvider.warningOrange;
        text = isArabic ? 'في انتظار الدفع' : 'Payment Pending';
        icon = Icons.payment;
        break;
      case OrderStatus.paymentConfirmed:
      case OrderStatus.inProgress:
        color = ThemeProvider.primaryBlue;
        text = isArabic ? 'قيد التنفيذ' : 'In Progress';
        icon = Icons.work;
        break;
      case OrderStatus.submitted:
        color = ThemeProvider.primaryBlue;
        text = isArabic ? 'تم التسليم للمراجعة' : 'Submitted for Review';
        icon = Icons.upload;
        break;
      case OrderStatus.delivered:
        color = ThemeProvider.successGreen;
        text = isArabic ? 'مكتمل' : 'Completed';
        icon = Icons.check_circle;
        break;
      case OrderStatus.editing:
        color = Colors.purple;
        text = isArabic ? 'قيد التعديل' : 'Under Revision';
        icon = Icons.edit;
        break;
      case OrderStatus.completed:
        color = ThemeProvider.successGreen;
        text = isArabic ? 'مكتمل' : 'Completed';
        icon = Icons.check_circle;
        break;
      case OrderStatus.cancelled:
        color = Colors.red;
        text = isArabic ? 'ملغي' : 'Cancelled';
        icon = Icons.cancel;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: color),
          const SizedBox(width: 6),
          Text(text, style: TextStyle(color: color, fontWeight: FontWeight.bold, fontSize: 12)),
        ],
      ),
    );
  }

  // Build Freelancer Info
  Widget _buildFreelancerInfo(OrderModel order, bool isArabic, bool isDark) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: (isDark ? ThemeProvider.darkCardBackground : Colors.grey[50]),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          CircleAvatar(
            radius: 20,
            backgroundColor: ThemeProvider.primaryBlue.withValues(alpha: 0.1),
            child: const Icon(Icons.person, color: ThemeProvider.primaryBlue, size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  isArabic ? 'المستقل' : 'Freelancer',
                  style: TextStyle(fontSize: 12, color: isDark ? ThemeProvider.darkTextSecondary : Colors.grey[600]),
                ),
                const SizedBox(height: 2),
                Text(
                  _safeSubstring(order.freelancerId, 12),
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    color: isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
                  ),
                ),
              ],
            ),
          ),
          // Rating placeholder
          Row(
            children: [
              const Icon(Icons.star, color: Colors.amber, size: 16),
              const SizedBox(width: 4),
              Text(
                '4.8',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  color: isDark ? ThemeProvider.darkTextPrimary : Colors.black87,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Build Mini Timeline
  Widget _buildMiniTimeline(OrderModel order, bool isArabic, bool isDark) {
    final steps = _getTimelineSteps(order, isArabic);
    final currentStepIndex = _getCurrentStepIndex(order.status);

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: (isDark ? ThemeProvider.darkCardBackground : Colors.grey[50]),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: List.generate(steps.length, (index) {
          final isCompleted = index <= currentStepIndex;
          final isLast = index == steps.length - 1;

          return Expanded(
            child: Row(
              children: [
                Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: isCompleted ? ThemeProvider.primaryBlue : Colors.grey[300],
                    shape: BoxShape.circle,
                  ),
                  child: Icon(isCompleted ? Icons.check : Icons.circle, size: 12, color: Colors.white),
                ),
                if (!isLast)
                  Expanded(
                    child: Container(height: 2, color: isCompleted ? ThemeProvider.primaryBlue : Colors.grey[300]),
                  ),
              ],
            ),
          );
        }),
      ),
    );
  }

  // Build Order Action Buttons
  Widget _buildOrderActionButtons(OrderModel order, bool isArabic, bool isDark, AppLocalizations l10n) {
    return Row(
      children: [
        // Chat Button
        Expanded(
          flex: 1,
          child: EnhancedButton(
            text: isArabic ? 'محادثة' : 'Chat',
            icon: Icons.chat,
            backgroundColor: ThemeProvider.primaryBlue,
            onPressed: () => _openChat(order, isArabic),
          ),
        ),
        const SizedBox(width: 8),
        // View Details Button
        Expanded(
          flex: 1,
          child: EnhancedButton(
            text: isArabic ? 'تفاصيل' : 'Details',
            icon: Icons.visibility,
            backgroundColor: Colors.grey[600]!,
            onPressed: () => _viewOrderDetails(order),
          ),
        ),
        const SizedBox(width: 8),
        // Dynamic Action Button
        Expanded(flex: 1, child: _buildDynamicActionButton(order, isArabic, isDark, l10n)),
      ],
    );
  }

  // Helper methods
  List<String> _getTimelineSteps(OrderModel order, bool isArabic) {
    if (isArabic) {
      return ['إنشاء', 'دفع', 'تنفيذ', 'تسليم', 'إكمال'];
    } else {
      return ['Created', 'Payment', 'Progress', 'Delivery', 'Complete'];
    }
  }

  int _getCurrentStepIndex(OrderStatus status) {
    switch (status) {
      case OrderStatus.created:
        return 0;
      case OrderStatus.paymentPending:
        return 0;
      case OrderStatus.paymentConfirmed:
      case OrderStatus.inProgress:
        return 2;
      case OrderStatus.submitted:
        return 3;
      case OrderStatus.delivered:
      case OrderStatus.editing:
        return 3;
      case OrderStatus.completed:
        return 4;
      case OrderStatus.cancelled:
        return 0;
    }
  }

  String _formatDateTime(DateTime dateTime, bool isArabic) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return isArabic ? 'منذ ${difference.inDays} يوم' : '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return isArabic ? 'منذ ${difference.inHours} ساعة' : '${difference.inHours}h ago';
    } else {
      return isArabic ? 'منذ ${difference.inMinutes} دقيقة' : '${difference.inMinutes}m ago';
    }
  }

  void _openChat(OrderModel order, bool isArabic) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => ChatScreen(
              chatId: order.id,
              recipientName: isArabic ? 'المستقل' : 'Freelancer',
              requestTitle: isArabic ? 'طلب رقم ${order.id}' : 'Order #${order.id}',
              isAdminChat: false,
              orderId: order.id, // Pass the order ID
            ),
      ),
    );
  }

  void _viewOrderDetails(OrderModel order) {
    // Navigate to chat screen instead of order details
    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => ChatScreen(
              chatId: 'order_${order.id}',
              recipientName: 'Freelancer',
              requestTitle: 'Order #${order.id}',
              orderId: order.id, // Pass the order ID
            ),
      ),
    );
  }

  Widget _buildDynamicActionButton(OrderModel order, bool isArabic, bool isDark, AppLocalizations l10n) {
    // Return different buttons based on order status
    switch (order.status) {
      case OrderStatus.paymentPending:
        return EnhancedButton(
          text: isArabic ? 'رفع الدفع' : 'Upload Payment',
          icon: Icons.upload,
          backgroundColor: ThemeProvider.warningOrange,
          onPressed: () => _uploadPaymentProof(order),
        );
      case OrderStatus.delivered:
        return EnhancedButton(
          text: isArabic ? 'قبول' : 'Accept Work',
          icon: Icons.check,
          backgroundColor: ThemeProvider.successGreen,
          onPressed: () => _acceptWork(order),
        );
      case OrderStatus.completed:
        return EnhancedButton(
          text: isArabic ? 'تقييم' : 'Rate',
          icon: Icons.star,
          backgroundColor: Colors.amber,
          onPressed: () => _rateFreelancer(order),
        );
      default:
        return EnhancedButton(
          text: isArabic ? 'تتبع' : 'Track',
          icon: Icons.timeline,
          backgroundColor: Colors.grey[600]!,
          onPressed: () => _viewOrderDetails(order),
        );
    }
  }

  // Action methods (placeholders)
  void _uploadPaymentProof(OrderModel order) async {
    final languageProvider = Provider.of<LanguageProvider>(context, listen: false);
    final isArabic = languageProvider.isArabic;

    try {
      // Show loading dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder:
            (context) => AlertDialog(
              content: Row(
                children: [
                  const CircularProgressIndicator(),
                  const SizedBox(width: 16),
                  Text(isArabic ? 'جاري الرفع...' : 'Uploading...'),
                ],
              ),
            ),
      );

      // Pick image from gallery or camera
      final image = await _showImageSourceDialog();
      if (image == null) {
        if (mounted) Navigator.of(context).pop(); // Close loading dialog
        return;
      }

      // Upload image to storage
      final imageUrl = await StorageService.uploadImage(image, 'payment_proofs');

      if (imageUrl != null) {
        // Update order with payment proof
        final updatedOrder = await OrderService.uploadPaymentProof(order.id, imageUrl);

        // Update local orders list
        if (mounted) {
          setState(() {
            final index = _orders.indexWhere((o) => o.id == order.id);
            if (index != -1) {
              _orders[index] = updatedOrder;
            }
          });

          Navigator.of(context).pop(); // Close loading dialog

          // Show success message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                isArabic
                    ? 'تم رفع إثبات الدفع بنجاح. سيتم مراجعته من قبل الإدارة.'
                    : 'Payment proof uploaded successfully. It will be reviewed by admin.',
              ),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      } else {
        if (mounted) Navigator.of(context).pop(); // Close loading dialog
        throw Exception('Failed to upload image');
      }
    } catch (e) {
      if (mounted) {
        Navigator.of(context).pop(); // Close loading dialog

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(isArabic ? 'خطأ في رفع إثبات الدفع: $e' : 'Error uploading payment proof: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  Future<File?> _showImageSourceDialog() async {
    final languageProvider = Provider.of<LanguageProvider>(context, listen: false);
    final isArabic = languageProvider.isArabic;

    final ImageSource? source = await showDialog<ImageSource>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(isArabic ? 'اختيار مصدر الصورة' : 'Choose Image Source'),
            content: Text(
              isArabic
                  ? 'من أين تريد اختيار صورة إثبات الدفع؟'
                  : 'Where would you like to choose the payment proof image from?',
            ),
            actions: [
              TextButton(onPressed: () => Navigator.pop(context), child: Text(isArabic ? 'إلغاء' : 'Cancel')),
              TextButton.icon(
                onPressed: () => Navigator.pop(context, ImageSource.camera),
                icon: const Icon(Icons.camera_alt),
                label: Text(isArabic ? 'الكاميرا' : 'Camera'),
              ),
              TextButton.icon(
                onPressed: () => Navigator.pop(context, ImageSource.gallery),
                icon: const Icon(Icons.photo_library),
                label: Text(isArabic ? 'المعرض' : 'Gallery'),
              ),
            ],
          ),
    );

    if (source != null) {
      try {
        return await StorageService.pickImage(source: source);
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  void _acceptWork(OrderModel order) async {
    final languageProvider = Provider.of<LanguageProvider>(context, listen: false);
    final isArabic = languageProvider.isArabic;

    // Show confirmation dialog
    final bool? confirmed = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(isArabic ? 'قبول العمل' : 'Accept Work', style: const TextStyle(fontWeight: FontWeight.bold)),
          content: Text(
            isArabic
                ? 'هل أنت متأكد من قبول هذا العمل؟ سيتم تحديث حالة الطلب إلى مكتمل.'
                : 'Are you sure you want to accept this work? The order status will be updated to completed.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text(isArabic ? 'إلغاء' : 'Cancel', style: TextStyle(color: Colors.grey[600])),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(backgroundColor: ThemeProvider.successGreen),
              child: Text(isArabic ? 'قبول' : 'Accept', style: const TextStyle(color: Colors.white)),
            ),
          ],
        );
      },
    );

    if (confirmed == true && mounted) {
      // Show loading dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return AlertDialog(
            content: Row(
              children: [
                const CircularProgressIndicator(),
                const SizedBox(width: 20),
                Text(isArabic ? 'جاري قبول العمل...' : 'Accepting work...'),
              ],
            ),
          );
        },
      );

      try {
        // Accept the work by completing the order with a default rating
        // Note: In a real app, you might want to show a rating dialog first
        final updatedOrder = await OrderService.completeOrder(
          order.id,
          5.0, // Default rating - could be made configurable
          isArabic ? 'تم قبول العمل' : 'Work accepted',
        );

        // Update local orders list
        if (mounted) {
          setState(() {
            final index = _orders.indexWhere((o) => o.id == order.id);
            if (index != -1) {
              _orders[index] = updatedOrder;
            }
          });

          Navigator.of(context).pop(); // Close loading dialog

          // Show success message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                isArabic
                    ? 'تم قبول العمل بنجاح! يمكنك الآن تقييم المستقل.'
                    : 'Work accepted successfully! You can now rate the freelancer.',
              ),
              backgroundColor: ThemeProvider.successGreen,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          Navigator.of(context).pop(); // Close loading dialog

          // Show error message
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                isArabic
                    ? 'حدث خطأ أثناء قبول العمل. يرجى المحاولة مرة أخرى.'
                    : 'An error occurred while accepting the work. Please try again.',
              ),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      }
    }
  }

  /// Get freelancer information by ID
  Future<UserModel?> _getFreelancerInfo(String freelancerId) async {
    try {
      // For demo purposes, create a demo freelancer based on the freelancerId
      // In production, this would fetch from the database
      return UserModel(
        id: freelancerId,
        email: '<EMAIL>',
        fullName: 'Demo Freelancer',
        role: UserRole.freelancer,
        rating: 4.8,
        completedJobs: 25,
        bio: 'Experienced Flutter developer with 5+ years of experience',
        skills: ['Flutter', 'Dart', 'Firebase', 'UI/UX Design'],
        isVerified: true,
        createdAt: DateTime.now().subtract(const Duration(days: 365)),
      );
    } catch (e) {
      return null;
    }
  }

  void _rateFreelancer(OrderModel order) async {
    final languageProvider = Provider.of<LanguageProvider>(context, listen: false);
    final isArabic = languageProvider.isArabic;

    // Check if order is already rated
    if (order.clientRating != null) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(isArabic ? 'لقد قمت بتقييم هذا الطلب مسبقاً' : 'You have already rated this order'),
            backgroundColor: Colors.orange,
            duration: const Duration(seconds: 3),
          ),
        );
      }
      return;
    }

    // Check if order is completed
    if (order.status != OrderStatus.completed) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(isArabic ? 'يمكن تقييم الطلب فقط بعد اكتماله' : 'You can only rate completed orders'),
            backgroundColor: Colors.orange,
            duration: const Duration(seconds: 3),
          ),
        );
      }
      return;
    }

    try {
      // Get freelancer information
      final freelancer = await _getFreelancerInfo(order.freelancerId);

      if (freelancer == null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(isArabic ? 'لا يمكن العثور على معلومات المستقل' : 'Could not find freelancer information'),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 3),
            ),
          );
        }
        return;
      }

      if (!mounted) return;

      // Navigate to submit review screen
      final result = await Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => SubmitReviewScreen(order: order, freelancer: freelancer)),
      );

      // If review was submitted successfully, refresh the orders
      if (result == true && mounted) {
        _loadOrders();

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(isArabic ? 'تم إرسال التقييم بنجاح' : 'Review submitted successfully'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      // Handle navigation error
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isArabic ? 'حدث خطأ أثناء فتح صفحة التقييم' : 'An error occurred while opening the review page',
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  // Helper method to format date
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  // Helper method to get time ago
  String _getTimeAgo(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays} ${difference.inDays == 1 ? 'day' : 'days'} ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} ${difference.inHours == 1 ? 'hour' : 'hours'} ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} ${difference.inMinutes == 1 ? 'minute' : 'minutes'} ago';
    } else {
      return 'Just now';
    }
  }
}
